#!/usr/bin/env python3
"""
Test script for ESSL Registration Dashboard
Tests the staff registration and fingerprint enrollment functionality
"""

import requests
import json
import time

# Test configuration
BASE_URL = 'http://localhost:5000'
TEST_STAFF_ID = 'EMP001'
TEST_STAFF_NAME = '<PERSON>'
TEST_DEVICE_IP = '*************'

def test_dashboard_page():
    """Test if the dashboard page loads correctly"""
    try:
        response = requests.get(BASE_URL)
        if response.status_code == 200:
            print("✅ Dashboard page loads successfully")
            return True
        else:
            print(f"❌ Dashboard page failed with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Dashboard page test failed: {e}")
        return False

def test_staff_registration():
    """Test the staff registration endpoint"""
    try:
        data = {
            'staff_id': TEST_STAFF_ID,
            'staff_name': TEST_STAFF_NAME,
            'device_ip': TEST_DEVICE_IP
        }
        
        print(f"🔄 Testing staff registration for {TEST_STAFF_ID}...")
        response = requests.post(f'{BASE_URL}/register_staff', data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Staff registration successful")
                print(f"   Message: {result.get('message', 'No message')}")
                print(f"   Staff ID: {result.get('staff_id')}")
                print(f"   Device IP: {result.get('device_ip')}")
                print(f"   Database Stored: {result.get('database_stored', False)}")
                return True
            else:
                print(f"❌ Staff registration failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ Registration endpoint failed with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Staff registration test failed: {e}")
        return False

def test_registration_status():
    """Test the registration status check endpoint"""
    try:
        data = {
            'staff_id': TEST_STAFF_ID,
            'device_ip': TEST_DEVICE_IP
        }
        
        print(f"🔄 Checking registration status for {TEST_STAFF_ID}...")
        response = requests.post(f'{BASE_URL}/check_registration_status', data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Registration status check successful")
                print(f"   Staff ID: {result.get('staff_id')}")
                print(f"   Staff Name: {result.get('staff_name')}")
                print(f"   Database Enrolled: {result.get('database_enrolled')}")
                print(f"   Biometric Enrolled: {result.get('biometric_enrolled')}")
                print(f"   Device Enrolled: {result.get('device_enrolled')}")
                print(f"   Registration Date: {result.get('registration_date')}")
                return True
            else:
                print(f"❌ Status check failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ Status check endpoint failed with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Registration status test failed: {e}")
        return False

def test_different_staff_ids():
    """Test registration with different staff IDs"""
    test_cases = [
        {'staff_id': 'EMP002', 'staff_name': 'Jane Smith'},
        {'staff_id': 'STAFF123', 'staff_name': 'Bob Johnson'},
        {'staff_id': 'ID999', 'staff_name': 'Alice Brown'}
    ]
    
    print("🔄 Testing multiple staff registrations...")
    success_count = 0
    
    for case in test_cases:
        try:
            data = {
                'staff_id': case['staff_id'],
                'staff_name': case['staff_name'],
                'device_ip': TEST_DEVICE_IP
            }
            
            response = requests.post(f'{BASE_URL}/register_staff', data=data)
            result = response.json()
            
            if result.get('success'):
                print(f"   ✅ {case['staff_id']} registered successfully")
                success_count += 1
            else:
                print(f"   ❌ {case['staff_id']} registration failed: {result.get('error', 'Unknown')}")
                
        except Exception as e:
            print(f"   ❌ {case['staff_id']} registration error: {e}")
    
    print(f"📊 Multiple registration test: {success_count}/{len(test_cases)} successful")
    return success_count == len(test_cases)

def test_invalid_inputs():
    """Test registration with invalid inputs"""
    print("🔄 Testing invalid input handling...")
    
    test_cases = [
        {'data': {'staff_id': '', 'device_ip': TEST_DEVICE_IP}, 'description': 'Empty Staff ID'},
        {'data': {'staff_id': TEST_STAFF_ID, 'device_ip': ''}, 'description': 'Empty Device IP'},
        {'data': {'device_ip': TEST_DEVICE_IP}, 'description': 'Missing Staff ID'},
        {'data': {'staff_id': TEST_STAFF_ID}, 'description': 'Missing Device IP'}
    ]
    
    success_count = 0
    
    for case in test_cases:
        try:
            response = requests.post(f'{BASE_URL}/register_staff', data=case['data'])
            result = response.json()
            
            if not result.get('success'):
                print(f"   ✅ {case['description']}: Properly rejected")
                success_count += 1
            else:
                print(f"   ❌ {case['description']}: Should have been rejected")
                
        except Exception as e:
            print(f"   ❌ {case['description']}: Error testing - {e}")
    
    print(f"📊 Invalid input test: {success_count}/{len(test_cases)} handled correctly")
    return success_count == len(test_cases)

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 ESSL Registration Dashboard - Test Suite")
    print("=" * 60)
    
    tests = [
        ('Dashboard Page Load', test_dashboard_page),
        ('Staff Registration', test_staff_registration),
        ('Registration Status Check', test_registration_status),
        ('Multiple Staff Registration', test_different_staff_ids),
        ('Invalid Input Handling', test_invalid_inputs)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! ESSL Registration Dashboard is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    print("\n📝 Notes:")
    print("• Device connection tests will show errors if no ESSL device is connected")
    print("• This is expected behavior when testing without hardware")
    print("• Database operations should work regardless of device connectivity")
    print("• Make sure the Flask app is running on http://localhost:5000")

if __name__ == '__main__':
    main()
