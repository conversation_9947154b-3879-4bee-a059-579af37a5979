#!/usr/bin/env python3
"""
Quick fix for database column issues
"""

import sqlite3
import os

DATABASE = 'vishnorex.db'

def fix_database():
    """Add missing columns to staff table"""
    print("🔧 Fixing database columns...")
    
    if not os.path.exists(DATABASE):
        print(f"❌ Database {DATABASE} not found.")
        return False
    
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # Check current columns
        cursor.execute("PRAGMA table_info(staff)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"Current columns: {columns}")
        
        # Add missing columns one by one
        columns_to_add = [
            'device_ip TEXT',
            'essl_user_id TEXT', 
            'fingerprint_enrolled BOOLEAN DEFAULT 0',
            'updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ]
        
        for column_def in columns_to_add:
            column_name = column_def.split()[0]
            if column_name not in columns:
                try:
                    cursor.execute(f'ALTER TABLE staff ADD COLUMN {column_def}')
                    print(f"✅ Added: {column_name}")
                except Exception as e:
                    print(f"⚠️  Failed to add {column_name}: {e}")
            else:
                print(f"✅ {column_name} already exists")
        
        conn.commit()
        conn.close()
        print("✅ Database fix completed!")
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        return False

if __name__ == '__main__':
    fix_database()
