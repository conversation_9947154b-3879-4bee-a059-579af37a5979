# database.py - Simplified for user registration and ESSL fingerprint integration
import sqlite3
from flask import g
import os

DATABASE = 'vishnorex.db'

def get_db():
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect(DATABASE)
        db.row_factory = sqlite3.Row
    return db

def init_db(app):
    os.makedirs(app.instance_path, exist_ok=True)

    with app.app_context():
        db = get_db()
        cursor = db.cursor()

        # Essential tables for user registration and ESSL fingerprint integration

        # Organizations/Schools table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS schools (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            address TEXT,
            contact_email TEXT,
            contact_phone TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Staff/Users table for ESSL registration
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS staff (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            school_id INTEGER DEFAULT 1,
            staff_id TEXT NOT NULL UNIQUE,
            full_name TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            department TEXT,
            position TEXT,
            password_hash TEXT,
            biometric_enrolled BOOLEAN DEFAULT 0,
            device_ip TEXT,
            essl_user_id TEXT,
            fingerprint_enrolled BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (school_id) REFERENCES schools(id)
        )
        ''')

        # Add new columns to existing staff table if they don't exist
        try:
            cursor.execute('ALTER TABLE staff ADD COLUMN device_ip TEXT')
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            cursor.execute('ALTER TABLE staff ADD COLUMN essl_user_id TEXT')
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            cursor.execute('ALTER TABLE staff ADD COLUMN fingerprint_enrolled BOOLEAN DEFAULT 0')
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            cursor.execute('ALTER TABLE staff ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
        except sqlite3.OperationalError:
            pass  # Column already exists

        # ESSL registration and verification log table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS essl_registrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            staff_id INTEGER NOT NULL,
            staff_code TEXT NOT NULL,
            device_ip TEXT NOT NULL,
            registration_status TEXT CHECK(registration_status IN ('pending', 'enrolled', 'failed')) DEFAULT 'pending',
            fingerprint_status TEXT CHECK(fingerprint_status IN ('pending', 'enrolled', 'failed')) DEFAULT 'pending',
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            error_message TEXT,
            FOREIGN KEY (staff_id) REFERENCES staff(id)
        )
        ''')

        # ESSL biometric verification log table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS biometric_verifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            staff_id INTEGER NOT NULL,
            school_id INTEGER NOT NULL,
            verification_type TEXT CHECK(verification_type IN ('check-in', 'check-out', 'overtime-in', 'overtime-out')) NOT NULL,
            verification_time DATETIME NOT NULL,
            device_ip TEXT,
            biometric_method TEXT CHECK(biometric_method IN ('fingerprint', 'face', 'card', 'password')),
            verification_status TEXT CHECK(verification_status IN ('success', 'failed', 'retry')) DEFAULT 'success',
            notes TEXT,
            FOREIGN KEY (staff_id) REFERENCES staff(id),
            FOREIGN KEY (school_id) REFERENCES schools(id)
        )
        ''')

        # Safe column additions for essential functionality
        def ensure_column_exists(table, column_def, column_name):
            cursor.execute(f"PRAGMA table_info({table})")
            columns = [col[1] for col in cursor.fetchall()]
            if column_name not in columns:
                cursor.execute(f"ALTER TABLE {table} ADD COLUMN {column_def}")

        # Add essential columns if they don't exist
        ensure_column_exists('staff', 'biometric_enrolled BOOLEAN DEFAULT 0', 'biometric_enrolled')
        ensure_column_exists('staff', 'password_hash TEXT', 'password_hash')

        db.commit()
