#!/usr/bin/env python3
"""
Test script for ESSL Fingerprint Enrollment System
"""

import requests
import json

# Test configuration
BASE_URL = 'http://localhost:5000'
TEST_STAFF_ID = 'TEST001'
TEST_DEVICE_IP = '*************'

def test_home_page():
    """Test if the home page loads correctly"""
    try:
        response = requests.get(BASE_URL)
        if response.status_code == 200:
            print("✓ Home page loads successfully")
            return True
        else:
            print(f"✗ Home page failed with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Home page test failed: {e}")
        return False

def test_enrollment_endpoint():
    """Test the enrollment endpoint"""
    try:
        data = {
            'staff_id': TEST_STAFF_ID,
            'device_ip': TEST_DEVICE_IP
        }
        
        response = requests.post(f'{BASE_URL}/enroll_biometric', data=data)
        
        if response.status_code == 200:
            result = response.json()
            if 'success' in result:
                print(f"✓ Enrollment endpoint responds correctly")
                print(f"  Response: {result.get('message', 'No message')}")
                return True
            else:
                print("✗ Enrollment endpoint returned invalid JSON")
                return False
        else:
            print(f"✗ Enrollment endpoint failed with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Enrollment endpoint test failed: {e}")
        return False

def test_verification_endpoint():
    """Test the verification endpoint"""
    try:
        data = {
            'staff_id': TEST_STAFF_ID,
            'device_ip': TEST_DEVICE_IP
        }
        
        response = requests.post(f'{BASE_URL}/verify_biometric', data=data)
        
        if response.status_code == 200:
            result = response.json()
            if 'success' in result:
                print(f"✓ Verification endpoint responds correctly")
                print(f"  Response: {result.get('message', 'No message')}")
                return True
            else:
                print("✗ Verification endpoint returned invalid JSON")
                return False
        else:
            print(f"✗ Verification endpoint failed with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Verification endpoint test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("ESSL Fingerprint Enrollment System - Test Suite")
    print("=" * 50)
    
    tests = [
        test_home_page,
        test_enrollment_endpoint,
        test_verification_endpoint
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    print("\nNote: Device connection tests will fail if no ESSL device is connected.")
    print("This is expected behavior when testing without hardware.")

if __name__ == '__main__':
    main()
