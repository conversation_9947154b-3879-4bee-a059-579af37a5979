<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESSL Registration Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .dashboard-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .main-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
            backdrop-filter: blur(15px);
            background: rgba(255,255,255,0.95);
            max-width: 600px;
            width: 100%;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 30px;
            text-align: center;
            border: none;
        }
        .fingerprint-icon {
            font-size: 4rem;
            margin-bottom: 15px;
            color: #fff;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .btn-check {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 25px;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-check:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            color: white;
        }
        .status-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 12px;
            display: none;
        }
        .status-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .device-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        .registration-steps {
            background: #e3f2fd;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }
        .step {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .step-number {
            background: #667eea;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12">
                    <div class="main-card">
                        <div class="card-header">
                            <div class="fingerprint-icon">
                                <i class="fas fa-fingerprint"></i>
                            </div>
                            <h2 class="mb-0">ESSL Registration Dashboard</h2>
                            <p class="mb-0 mt-2">Enter Staff ID and Device IP for Fingerprint Registration</p>
                        </div>
                        <div class="card-body p-4">
                            <!-- Registration Form -->
                            <form id="registrationForm">
                                <div class="row">
                                    <div class="col-md-6 mb-4">
                                        <label for="staff_id" class="form-label fw-bold">
                                            <i class="fas fa-id-badge me-2"></i>Staff ID
                                        </label>
                                        <input type="text" class="form-control" id="staff_id" name="staff_id"
                                               placeholder="Enter Staff ID" required>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <label for="staff_name" class="form-label fw-bold">
                                            <i class="fas fa-user me-2"></i>Staff Name (Optional)
                                        </label>
                                        <input type="text" class="form-control" id="staff_name" name="staff_name"
                                               placeholder="Enter Staff Name">
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="device_ip" class="form-label fw-bold">
                                        <i class="fas fa-network-wired me-2"></i>ESSL Device IP Address
                                    </label>
                                    <input type="text" class="form-control" id="device_ip" name="device_ip"
                                           value="*************" placeholder="*************" required>
                                </div>

                                <div class="d-grid gap-3">
                                    <button type="submit" class="btn btn-register btn-lg">
                                        <i class="fas fa-fingerprint me-2"></i>
                                        Register Staff & Enroll Fingerprint
                                    </button>
                                    <button type="button" class="btn btn-check" onclick="checkRegistrationStatus()">
                                        <i class="fas fa-search me-2"></i>
                                        Check Registration Status
                                    </button>
                                </div>
                            </form>

                            <!-- Loading Spinner -->
                            <div id="loadingSpinner" class="loading-spinner">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Processing...</span>
                                </div>
                                <p class="mt-2">Connecting to ESSL device...</p>
                            </div>

                            <!-- Status Message -->
                            <div id="statusMessage" class="status-message"></div>

                            <!-- Device Information -->
                            <div class="device-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Device Information</h6>
                                <p class="mb-1"><strong>Default IP:</strong> *************</p>
                                <p class="mb-0"><strong>Port:</strong> 4370 (Standard ESSL Port)</p>
                            </div>

                            <!-- Registration Steps -->
                            <div class="registration-steps">
                                <h6><i class="fas fa-list-ol me-2"></i>Registration Process</h6>
                                <div class="step">
                                    <div class="step-number">1</div>
                                    <span>Enter Staff ID and Device IP</span>
                                </div>
                                <div class="step">
                                    <div class="step-number">2</div>
                                    <span>Click "Register Staff & Enroll Fingerprint"</span>
                                </div>
                                <div class="step">
                                    <div class="step-number">3</div>
                                    <span>Complete fingerprint scanning on ESSL device</span>
                                </div>
                                <div class="step">
                                    <div class="step-number">4</div>
                                    <span>Data automatically stored in ESSL database</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Registration Form Handler
        document.getElementById('registrationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const staffId = document.getElementById('staff_id').value.trim();
            const staffName = document.getElementById('staff_name').value.trim();
            const deviceIp = document.getElementById('device_ip').value.trim();
            
            if (!staffId) {
                showMessage('Please enter a Staff ID', 'error');
                return;
            }
            
            if (!deviceIp) {
                showMessage('Please enter Device IP Address', 'error');
                return;
            }
            
            registerStaff(staffId, staffName, deviceIp);
        });

        function registerStaff(staffId, staffName, deviceIp) {
            showLoading(true);
            hideMessage();
            
            const formData = new FormData();
            formData.append('staff_id', staffId);
            formData.append('staff_name', staffName || `Staff_${staffId}`);
            formData.append('device_ip', deviceIp);
            
            fetch('/register_staff', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                
                if (data.success) {
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.error || 'Registration failed', 'error');
                }
            })
            .catch(error => {
                showLoading(false);
                showMessage('Network error: ' + error.message, 'error');
            });
        }

        function checkRegistrationStatus() {
            const staffId = document.getElementById('staff_id').value.trim();
            const deviceIp = document.getElementById('device_ip').value.trim();
            
            if (!staffId) {
                showMessage('Please enter a Staff ID to check status', 'error');
                return;
            }
            
            showLoading(true);
            hideMessage();
            
            const formData = new FormData();
            formData.append('staff_id', staffId);
            formData.append('device_ip', deviceIp);
            
            fetch('/check_registration_status', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showLoading(false);
                
                if (data.success) {
                    const statusHtml = `
                        <strong>Registration Status for ${data.staff_id}:</strong><br>
                        <i class="fas fa-user me-2"></i>Name: ${data.staff_name}<br>
                        <i class="fas fa-database me-2"></i>Database: ${data.database_enrolled ? 'Enrolled' : 'Not Enrolled'}<br>
                        <i class="fas fa-fingerprint me-2"></i>Biometric: ${data.biometric_enrolled ? 'Enrolled' : 'Not Enrolled'}<br>
                        <i class="fas fa-desktop me-2"></i>Device: ${data.device_enrolled ? 'Enrolled' : 'Not Enrolled'}<br>
                        <i class="fas fa-calendar me-2"></i>Date: ${data.registration_date}
                    `;
                    showMessage(statusHtml, 'success');
                } else {
                    showMessage(data.error || 'Status check failed', 'error');
                }
            })
            .catch(error => {
                showLoading(false);
                showMessage('Network error: ' + error.message, 'error');
            });
        }

        function showLoading(show) {
            document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('statusMessage');
            messageDiv.innerHTML = message;
            messageDiv.className = `status-message status-${type}`;
            messageDiv.style.display = 'block';
        }

        function hideMessage() {
            document.getElementById('statusMessage').style.display = 'none';
        }
    </script>
</body>
</html>
