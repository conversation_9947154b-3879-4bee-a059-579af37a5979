#!/usr/bin/env python3
"""
Simplified ZK Biometric Device Integration Module
Handles connection to ESSL ZK biometric devices for user registration and fingerprint verification
"""

from zk import ZK, const
import datetime
import logging
from typing import List, Dict, Optional
from database import get_db

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ZKBiometricDevice:
    """Simplified ZK Biometric Device Handler for ESSL fingerprint scanners"""

    def __init__(self, device_ip: str = '*************', port: int = 4370, timeout: int = 5):
        self.device_ip = device_ip
        self.port = port
        self.timeout = timeout
        self.device_id = f"ZK_{device_ip.replace('.', '_')}"

        # Direct Ethernet connection only
        self.zk = ZK(device_ip, port=port, timeout=timeout)
        self.connection = None

        logger.info(f"ZK Device {self.device_id} initialized for direct Ethernet connection")

    def connect(self) -> bool:
        """Connect to ZK device via Ethernet"""
        try:
            self.connection = self.zk.connect()
            logger.info(f"Successfully connected to ZK device at {self.device_ip}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to ZK device: {str(e)}")
            return False

    def disconnect(self):
        """Disconnect from ZK device"""
        if self.connection:
            try:
                self.connection.disconnect()
                logger.info("Disconnected from ZK device")
            except Exception as e:
                logger.error(f"Error disconnecting from ZK device: {str(e)}")

    def get_attendance_records(self) -> List[Dict]:
        """Get attendance records from ZK device"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return []

        try:
            # Disable device to prevent interference
            self.connection.disable_device()

            # Get attendance records
            attendance = self.connection.get_attendance()
            records = []

            for record in attendance:
                # Map punch codes to our verification types
                verification_type = self._map_punch_to_verification_type(record.punch)

                records.append({
                    'user_id': record.user_id,
                    'timestamp': record.timestamp,
                    'status': record.status,
                    'punch': record.punch,  # Original punch code
                    'verification_type': verification_type,  # Mapped verification type
                    'verify': getattr(record, 'verify', 0)  # Verification method (safe access)
                })

            # Re-enable device
            self.connection.enable_device()

            logger.info(f"Retrieved {len(records)} attendance records from ZK device")
            return records

        except Exception as e:
            logger.error(f"Error getting attendance records: {str(e)}")
            # Make sure to re-enable device even if error occurs
            try:
                self.connection.enable_device()
            except:
                pass
            return []

    def _map_punch_to_verification_type(self, punch_code: int) -> str:
        """
        Map ZK device punch codes to our verification types

        ZK Device punch codes:
        0 = Check In -> check-in
        1 = Check Out -> check-out
        2 = Break Out -> overtime-in (repurposed for overtime start)
        3 = Break In -> overtime-out (repurposed for overtime end)

        Args:
            punch_code: The punch code from ZK device

        Returns:
            String verification type
        """
        punch_mapping = {
            0: 'check-in',
            1: 'check-out',
            2: 'overtime-in',
            3: 'overtime-out'
        }

        return punch_mapping.get(punch_code, 'check-in')  # Default to check-in

    def get_new_attendance_records(self, since_timestamp: datetime.datetime = None) -> List[Dict]:
        """Get attendance records from ZK device since a specific timestamp"""
        all_records = self.get_attendance_records()

        if since_timestamp is None:
            return all_records

        # Filter records to only include those after the specified timestamp
        new_records = []
        for record in all_records:
            if record['timestamp'] > since_timestamp:
                new_records.append(record)

        return new_records
    
    def get_users(self) -> List[Dict]:
        """Get users from ZK device"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return []

        try:
            users = self.connection.get_users()
            user_list = []

            for user in users:
                # Handle both dict and object formats
                if isinstance(user, dict):
                    user_list.append(user)
                else:
                    user_list.append({
                        'uid': getattr(user, 'uid', 0),
                        'user_id': getattr(user, 'user_id', ''),
                        'name': getattr(user, 'name', ''),
                        'privilege': getattr(user, 'privilege', 0),
                        'password': getattr(user, 'password', ''),
                        'group_id': getattr(user, 'group_id', '0')
                    })

            logger.info(f"Retrieved {len(user_list)} users from ZK device")
            return user_list

        except Exception as e:
            logger.error(f"Error getting users: {str(e)}")
            return []
    
    def clear_attendance(self) -> bool:
        """Clear attendance records from ZK device"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return False

        try:
            self.connection.clear_attendance()
            logger.info("Cleared attendance records from ZK device")
            return True
        except Exception as e:
            logger.error(f"Error clearing attendance: {str(e)}")
            return False

    def enroll_user(self, user_id: str, name: str, privilege: int = 0, password: str = '', group_id: str = '0', overwrite: bool = False) -> dict:
        """Enroll a new user in the ZK device"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return {'success': False, 'message': 'No connection to ZK device', 'user_exists': False}

        try:
            # Disable device during enrollment
            self.connection.disable_device()

            # Check if user already exists
            existing_users = self.connection.get_users()
            existing_user = None
            for user in existing_users:
                # Handle both dict and object formats
                user_id_value = user.get('user_id') if isinstance(user, dict) else getattr(user, 'user_id', None)
                if str(user_id_value) == str(user_id):
                    existing_user = user
                    break

            if existing_user and not overwrite:
                logger.warning(f"User {user_id} already exists on device")
                self.connection.enable_device()
                return {
                    'success': False,
                    'message': f'User {user_id} already exists on device',
                    'user_exists': True
                }
            elif existing_user and overwrite:
                # Delete existing user first
                logger.info(f"Overwriting existing user {user_id}")
                user_uid = existing_user.get('uid') if isinstance(existing_user, dict) else getattr(existing_user, 'uid', None)
                if user_uid:
                    self.connection.delete_user(user_uid)

            # Get next available UID
            users = self.connection.get_users()
            max_uid = max([getattr(user, 'uid', 0) for user in users] + [0]) + 1

            # Create user using direct set_user call
            try:
                self.connection.set_user(
                    uid=int(max_uid),
                    name=str(name),
                    privilege=int(privilege),
                    password=str(password) if password else '',
                    group_id=str(group_id) if group_id else '0',
                    user_id=str(user_id)
                )
                logger.info(f"User {user_id} created with UID {max_uid}")
            except Exception as user_error:
                logger.error(f"Failed to create user: {user_error}")
                self.connection.enable_device()
                return {'success': False, 'message': f'Failed to create user: {str(user_error)}', 'user_exists': False}

            # Re-enable device
            self.connection.enable_device()

            action = "overwritten" if existing_user else "enrolled"
            logger.info(f"Successfully {action} user {user_id} ({name}) on ZK device")
            return {
                'success': True,
                'message': f'User {user_id} successfully {action} on device',
                'user_exists': existing_user is not None,
                'action': action
            }

        except Exception as e:
            logger.error(f"Error enrolling user {user_id}: {str(e)}")
            # Make sure to re-enable device
            try:
                self.connection.enable_device()
            except:
                pass
            return {'success': False, 'message': f'Error enrolling user: {str(e)}', 'user_exists': False}

    def delete_user(self, user_id: str) -> bool:
        """Delete a user from the ZK device"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return False

        try:
            # Disable device during deletion
            self.connection.disable_device()

            # Find and delete user
            users = self.connection.get_users()
            for user in users:
                # Handle both dict and object formats
                user_id_value = user.get('user_id') if isinstance(user, dict) else getattr(user, 'user_id', None)
                if str(user_id_value) == str(user_id):
                    user_uid = user.get('uid') if isinstance(user, dict) else getattr(user, 'uid', None)
                    self.connection.delete_user(user_uid)
                    logger.info(f"Successfully deleted user {user_id} from ZK device")
                    self.connection.enable_device()
                    return True

            # Re-enable device
            self.connection.enable_device()
            logger.warning(f"User {user_id} not found on device")
            return False

        except Exception as e:
            logger.error(f"Error deleting user {user_id}: {str(e)}")
            # Make sure to re-enable device
            try:
                self.connection.enable_device()
            except:
                pass
            return False



    def start_enrollment_mode(self, user_id: str = None) -> bool:
        """Put device in enrollment mode for biometric capture"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return False

        try:
            # Disable device to prevent normal operation during enrollment
            self.connection.disable_device()

            # Try to trigger enrollment mode using available ZK commands
            try:
                # Some ZK devices support direct enrollment commands
                # This will vary by device model and firmware
                if hasattr(self.connection, 'start_enroll'):
                    # If the device supports start_enroll command
                    if user_id:
                        self.connection.start_enroll(user_id)
                    else:
                        self.connection.start_enroll()
                    logger.info(f"Started enrollment mode for user {user_id if user_id else 'new user'}")
                elif hasattr(self.connection, 'enroll_user'):
                    # Alternative enrollment method
                    logger.info("Using alternative enrollment method")
                else:
                    # Fallback: Use device beep and voice prompts if available
                    if hasattr(self.connection, 'test_voice'):
                        try:
                            self.connection.test_voice()  # Voice prompt for enrollment
                        except:
                            pass

                    # Enable device temporarily to allow manual enrollment
                    self.connection.enable_device()
                    logger.info("Device enabled for manual biometric enrollment")
                    logger.info("Please use the device interface to enroll biometric data")
                    return True

            except Exception as enrollment_error:
                logger.warning(f"Direct enrollment command failed: {enrollment_error}")
                # Fallback to manual enrollment mode
                self.connection.enable_device()
                logger.info("Device enabled for manual biometric enrollment")

            logger.info("Device set to enrollment mode")
            return True

        except Exception as e:
            logger.error(f"Error setting enrollment mode: {str(e)}")
            # Make sure device is re-enabled if error occurs
            try:
                self.connection.enable_device()
            except:
                pass
            return False

    def end_enrollment_mode(self) -> bool:
        """Exit enrollment mode and return to normal operation"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return False

        try:
            # Stop any ongoing enrollment process
            try:
                if hasattr(self.connection, 'cancel_capture'):
                    self.connection.cancel_capture()
                elif hasattr(self.connection, 'stop_enroll'):
                    self.connection.stop_enroll()
            except Exception as stop_error:
                logger.warning(f"Could not stop enrollment process: {stop_error}")

            # Re-enable device for normal operation
            self.connection.enable_device()
            logger.info("Device returned to normal mode")
            return True
        except Exception as e:
            logger.error(f"Error ending enrollment mode: {str(e)}")
            return False

    def trigger_biometric_enrollment(self, user_id: str) -> dict:
        """Trigger biometric enrollment for a specific user"""
        if not self.connection:
            logger.error("No connection to ZK device")
            return {'success': False, 'message': 'No connection to ZK device'}

        try:
            # Enable device for manual enrollment
            self.connection.enable_device()

            # Use voice/beep prompts if available
            try:
                if hasattr(self.connection, 'test_voice'):
                    self.connection.test_voice()
                elif hasattr(self.connection, 'beep'):
                    # Beep pattern to indicate enrollment mode
                    for _ in range(3):
                        self.connection.beep()
            except:
                pass

            logger.info(f"Device ready for manual enrollment of user {user_id}")
            return {
                'success': True,
                'message': f'Device ready for manual biometric enrollment of user {user_id}. Please use device interface.',
                'manual_mode': True
            }

        except Exception as e:
            logger.error(f"Error triggering enrollment for user {user_id}: {str(e)}")
            return {'success': False, 'message': f'Error triggering enrollment: {str(e)}'}

# Essential verification function

def verify_staff_biometric(staff_id: str, device_ip: str = '*************', biometric_method: str = 'fingerprint') -> Dict:
    """
    Check for recent biometric verification from ZK device

    Args:
        staff_id: Staff ID to verify
        device_ip: IP address of ZK device
        biometric_method: Type of biometric verification (fingerprint, face, etc.)

    Returns:
        Dict with success status and message
    """
    result = {
        'success': False,
        'message': '',
        'staff_id': staff_id,
        'biometric_method': biometric_method
    }

    # Initialize ZK device
    zk_device = ZKBiometricDevice(device_ip)

    try:
        # Connect to device
        if not zk_device.connect():
            result['message'] = 'Failed to connect to biometric device'
            return result

        # Check if user exists on device
        users = zk_device.get_users()
        user_found = False

        for user in users:
            # Handle both dict and object formats
            user_id = user.get('user_id') if isinstance(user, dict) else getattr(user, 'user_id', None)
            if str(user_id) == str(staff_id):
                user_found = True
                break

        if not user_found:
            result['message'] = f'Staff ID {staff_id} not enrolled on biometric device'
            return result

        # Check for recent attendance records from the device (within last 30 seconds)
        recent_cutoff = datetime.datetime.now() - datetime.timedelta(seconds=30)
        recent_records = zk_device.get_new_attendance_records(recent_cutoff)

        # Look for a recent record for this staff member
        for record in recent_records:
            if str(record['user_id']) == str(staff_id):
                result['success'] = True
                result['message'] = f'Recent biometric verification found for staff ID {staff_id}'
                result['verification_type'] = record['verification_type']
                result['timestamp'] = record['timestamp']
                logger.info(f"Recent biometric verification found for staff {staff_id}: {record['verification_type']}")
                return result

        # If no recent verification found, return success for enrollment checking
        result['success'] = True
        result['message'] = f'Staff ID {staff_id} is enrolled and ready for biometric verification'

        logger.info(f"Staff {staff_id} is enrolled on biometric device")

    except Exception as e:
        result['message'] = f'Verification error: {str(e)}'
        logger.error(f"Biometric verification error for staff {staff_id}: {str(e)}")

    finally:
        # Always disconnect
        zk_device.disconnect()

    return result

if __name__ == '__main__':
    # Test the ZK device connection
    device = ZKBiometricDevice()
    if device.connect():
        print("Successfully connected to ZK device")
        users = device.get_users()
        print(f"Found {len(users)} users on device")
        device.disconnect()
    else:
        print("Failed to connect to ZK device")
