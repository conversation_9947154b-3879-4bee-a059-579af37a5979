# ESSL Fingerprint Enrollment System

A minimal single-page system for ESSL fingerprint scanner enrollment.

## 🚀 Core Features

- **Single Page Interface**: Simple form with only Staff ID and Device IP fields
- **ESSL Fingerprint Enrollment**: Direct enrollment on ESSL ZK biometric devices
- **Verification Testing**: Test enrolled fingerprints
- **Real-time Status**: Instant feedback on enrollment and verification

## 🛠️ Installation

1. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application**
   ```bash
   python app.py
   ```

3. **Access the application**
   Open your browser and go to `http://127.0.0.1:5000`

## 📁 Project Structure

```
├── app.py                 # Main Flask application
├── database.py           # Database configuration
├── zk_biometric.py       # ESSL device integration
├── requirements.txt      # Python dependencies
├── templates/
│   ├── index.html        # Home page
│   └── register.html     # Registration page
└── static/
    ├── images/           # Logo and images
    └── uploads/          # User photo uploads
```

## 🔧 Configuration

### ESSL Device Setup

1. Connect your ESSL ZK biometric device to the network
2. Configure the device IP address (default: *************)
3. Ensure the device is accessible from the server

## 🎯 Usage

### Fingerprint Enrollment

1. Access the system at `http://localhost:5000`
2. Enter the **Staff ID** (any alphanumeric identifier)
3. Enter the **Device IP Address** (default: *************)
4. Click **"Enroll Fingerprint"** to register the user on the device
5. Follow device prompts to capture fingerprint data
6. Use **"Test Verification"** to confirm enrollment was successful

## 🔌 ESSL Device Integration

### Supported Devices
- ESSL ZK devices with Ethernet connectivity
- Any ZK-compatible biometric device

### Device Configuration
- **Device IP**: ************* (default)
- **Port**: 4370
- **Connection**: Direct TCP/IP

## 📊 API Endpoints

### Core Functions
- `GET /` - Single page interface
- `POST /enroll_biometric` - Enroll user on biometric device
- `POST /verify_biometric` - Test biometric verification
- `POST /check_device_verification` - Check for recent device verification

## 🔧 Troubleshooting

### Device Connection Issues
- Verify device IP address and network connectivity
- Check device power and network settings
- Ensure firewall allows communication on port 4370

### Registration Issues
- Check database permissions
- Verify all required fields are filled
- Ensure organization is selected

## 📝 License

This project is a simplified version focused on core functionality.
