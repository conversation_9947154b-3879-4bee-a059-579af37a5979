#!/usr/bin/env python3
"""
Database migration script to add missing columns to existing staff table
"""

import sqlite3
import os

DATABASE = 'vishnorex.db'

def migrate_database():
    """Add missing columns to existing staff table"""
    print("🔄 Starting database migration...")
    
    if not os.path.exists(DATABASE):
        print(f"❌ Database {DATABASE} not found. Please run the main app first to create it.")
        return False
    
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        # Check current table structure
        cursor.execute("PRAGMA table_info(staff)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📊 Current staff table columns: {columns}")
        
        # Add missing columns
        migrations = [
            ('device_ip', 'ALTER TABLE staff ADD COLUMN device_ip TEXT'),
            ('essl_user_id', 'ALTER TABLE staff ADD COLUMN essl_user_id TEXT'),
            ('fingerprint_enrolled', 'ALTER TABLE staff ADD COLUMN fingerprint_enrolled BOOLEAN DEFAULT 0'),
            ('updated_at', 'ALTER TABLE staff ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
        ]
        
        for column_name, sql in migrations:
            if column_name not in columns:
                try:
                    cursor.execute(sql)
                    print(f"✅ Added column: {column_name}")
                except sqlite3.OperationalError as e:
                    print(f"⚠️  Column {column_name} migration failed: {e}")
            else:
                print(f"✅ Column {column_name} already exists")
        
        # Commit changes
        conn.commit()
        
        # Verify final structure
        cursor.execute("PRAGMA table_info(staff)")
        final_columns = [column[1] for column in cursor.fetchall()]
        print(f"📊 Final staff table columns: {final_columns}")
        
        conn.close()
        print("✅ Database migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def create_essl_registrations_table():
    """Create the ESSL registrations table if it doesn't exist"""
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS essl_registrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            staff_id INTEGER NOT NULL,
            staff_code TEXT NOT NULL,
            device_ip TEXT NOT NULL,
            registration_status TEXT CHECK(registration_status IN ('pending', 'enrolled', 'failed')) DEFAULT 'pending',
            fingerprint_status TEXT CHECK(fingerprint_status IN ('pending', 'enrolled', 'failed')) DEFAULT 'pending',
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            error_message TEXT,
            FOREIGN KEY (staff_id) REFERENCES staff(id)
        )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ ESSL registrations table created/verified")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create ESSL registrations table: {e}")
        return False

def main():
    """Run database migration"""
    print("=" * 50)
    print("🔧 ESSL Database Migration Tool")
    print("=" * 50)
    
    # Run migrations
    migration_success = migrate_database()
    table_success = create_essl_registrations_table()
    
    if migration_success and table_success:
        print("\n🎉 All migrations completed successfully!")
        print("✅ Your ESSL registration dashboard is ready to use.")
    else:
        print("\n⚠️  Some migrations failed. Please check the errors above.")
    
    print("\n📝 Next steps:")
    print("1. Restart your Flask application")
    print("2. Try registering a staff member")
    print("3. Check the dashboard at http://localhost:5000")

if __name__ == '__main__':
    main()
