# Simplified Flask App for ESSL Fingerprint Enrollment
from flask import Flask, render_template, request, jsonify, g
from flask_wtf.csrf import CSRFProtect
import os
import datetime
from database import get_db, init_db
from zk_biometric import ZKBiometricDevice, verify_staff_biometric

# Create Flask app instance
app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', os.urandom(24).hex())

# Initialize CSRF protection
csrf = CSRFProtect(app)

# Initialize database with the app
init_db(app)

# File upload configuration for user photos
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Database connection cleanup
@app.teardown_appcontext
def close_db(error):
    """Close database connection at the end of request"""
    _ = error  # Suppress unused parameter warning
    db = getattr(g, '_database', None)
    if db is not None:
        db.close()

# Main index route - Single page with Staff ID and Device IP
@app.route('/')
def index():
    return render_template('index.html')

# Biometric Enrollment Route
@app.route('/enroll_biometric', methods=['POST'])
@csrf.exempt
def enroll_biometric():
    staff_id = request.form.get('staff_id')
    device_ip = request.form.get('device_ip', '*************')

    if not staff_id:
        return jsonify({'success': False, 'error': 'Staff ID required'})

    try:
        # Initialize ZK device
        zk_device = ZKBiometricDevice(device_ip)

        if not zk_device.connect():
            return jsonify({'success': False, 'error': 'Failed to connect to biometric device'})

        # Enroll user on device (use staff_id as both user_id and name)
        result = zk_device.enroll_user(
            user_id=staff_id,
            name=f'User_{staff_id}',
            overwrite=True
        )

        if result['success']:
            # Trigger biometric enrollment mode
            enrollment_result = zk_device.trigger_biometric_enrollment(staff_id)

            zk_device.disconnect()

            return jsonify({
                'success': True,
                'message': f'User {staff_id} enrolled successfully. {enrollment_result.get("message", "Please complete biometric enrollment on device.")}',
                'enrollment_mode': enrollment_result.get('manual_mode', True)
            })
        else:
            zk_device.disconnect()
            return jsonify({'success': False, 'error': result['message']})

    except Exception as e:
        return jsonify({'success': False, 'error': f'Enrollment error: {str(e)}'})

# Biometric Verification Route
@app.route('/verify_biometric', methods=['POST'])
@csrf.exempt
def verify_biometric():
    staff_id = request.form.get('staff_id')
    device_ip = request.form.get('device_ip', '*************')
    
    if not staff_id:
        return jsonify({'success': False, 'error': 'Staff ID required'})
    
    try:
        result = verify_staff_biometric(staff_id, device_ip)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'error': f'Verification error: {str(e)}'})

# Check Device Verification Route
@app.route('/check_device_verification', methods=['POST'])
@csrf.exempt
def check_device_verification():
    staff_id = request.form.get('staff_id')
    device_ip = request.form.get('device_ip', '*************')
    
    if not staff_id:
        return jsonify({'success': False, 'error': 'Staff ID required'})
    
    try:
        # Check for recent biometric verification from the device
        zk_device = ZKBiometricDevice(device_ip)
        
        if not zk_device.connect():
            return jsonify({'success': False, 'error': 'Failed to connect to biometric device'})
        
        # Look for recent attendance records for this staff member (within last 30 seconds)
        recent_cutoff = datetime.datetime.now() - datetime.timedelta(seconds=30)
        recent_records = zk_device.get_new_attendance_records(recent_cutoff)
        
        staff_recent_record = None
        for record in recent_records:
            if str(record['user_id']) == str(staff_id):
                staff_recent_record = record
                break
        
        zk_device.disconnect()
        
        if staff_recent_record:
            # Log the verification
            db = get_db()
            staff_info = db.execute('SELECT id, school_id FROM staff WHERE staff_id = ?', (staff_id,)).fetchone()
            
            if staff_info:
                db.execute('''
                    INSERT INTO biometric_verifications
                    (staff_id, school_id, verification_type, verification_time, device_ip, biometric_method, verification_status)
                    VALUES (?, ?, ?, ?, ?, 'fingerprint', 'success')
                ''', (staff_info['id'], staff_info['school_id'], staff_recent_record['verification_type'], 
                      staff_recent_record['timestamp'], device_ip))
                db.commit()
            
            return jsonify({
                'success': True,
                'message': f'Biometric verification successful: {staff_recent_record["verification_type"]}',
                'verification_type': staff_recent_record['verification_type'],
                'timestamp': staff_recent_record['timestamp'].strftime('%Y-%m-%d %I:%M %p')
            })
        else:
            return jsonify({
                'success': False,
                'error': 'No recent biometric verification found. Please use the biometric device.'
            })
            
    except Exception as e:
        return jsonify({'success': False, 'error': f'Verification check error: {str(e)}'})



if __name__ == '__main__':
    print("Starting Simplified User Registration & ESSL Fingerprint System...")
    print("Server will be available at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)