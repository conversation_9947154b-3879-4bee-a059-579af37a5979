# ESSL Registration Dashboard - Simplified for Staff ID and Fingerprint Enrollment
from flask import Flask, render_template, request, jsonify, g
from flask_wtf.csrf import CSRFProtect
import os
import datetime
from database import get_db, init_db
from zk_biometric import ZKBiometricDevice

# Create Flask app instance
app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', os.urandom(24).hex())

# Initialize CSRF protection
csrf = CSRFProtect(app)

# Initialize database with the app
init_db(app)

# Database connection cleanup
@app.teardown_appcontext
def close_db(error):
    """Close database connection at the end of request"""
    _ = error  # Suppress unused parameter warning
    db = getattr(g, '_database', None)
    if db is not None:
        db.close()

# Main dashboard route
@app.route('/')
def dashboard():
    return render_template('essl_dashboard.html')

# ESSL Registration Route - Staff ID + IP + Fingerprint Enrollment
@app.route('/register_staff', methods=['POST'])
@csrf.exempt
def register_staff():
    staff_id = request.form.get('staff_id')
    device_ip = request.form.get('device_ip', '*************')
    staff_name = request.form.get('staff_name', f'Staff_{staff_id}')

    if not staff_id:
        return jsonify({'success': False, 'error': 'Staff ID is required'})

    if not device_ip:
        return jsonify({'success': False, 'error': 'Device IP is required'})

    try:
        # Step 1: Store staff data in database
        db = get_db()

        # Check if staff already exists
        existing_staff = db.execute(
            'SELECT id FROM staff WHERE staff_id = ?', (staff_id,)
        ).fetchone()

        if existing_staff:
            staff_db_id = existing_staff['id']
            # Update existing staff with new device IP
            db.execute('''
                UPDATE staff SET device_ip = ? WHERE id = ?
            ''', (device_ip, staff_db_id))
        else:
            # Insert new staff record
            cursor = db.execute('''
                INSERT INTO staff (staff_id, full_name, biometric_enrolled, created_at)
                VALUES (?, ?, 0, ?)
            ''', (staff_id, staff_name, datetime.datetime.now()))
            staff_db_id = cursor.lastrowid

        db.commit()

        # Step 2: Connect to ESSL device and enroll fingerprint
        zk_device = ZKBiometricDevice(device_ip)

        if not zk_device.connect():
            return jsonify({
                'success': False,
                'error': f'Failed to connect to ESSL device at {device_ip}. Please check IP address and device connectivity.'
            })

        # Step 3: Enroll user on ESSL device
        enrollment_result = zk_device.enroll_user(
            user_id=staff_id,
            name=staff_name,
            overwrite=True
        )

        if enrollment_result['success']:
            # Step 4: Trigger fingerprint enrollment mode
            fingerprint_result = zk_device.trigger_biometric_enrollment(staff_id)

            # Step 5: Update database to mark biometric as enrolled
            db.execute(
                'UPDATE staff SET biometric_enrolled = 1 WHERE id = ?',
                (staff_db_id,)
            )

            # Log the registration in ESSL registrations table (if table exists)
            try:
                db.execute('''
                    INSERT INTO essl_registrations
                    (staff_id, staff_code, device_ip, registration_status, fingerprint_status, registration_date)
                    VALUES (?, ?, ?, 'enrolled', 'enrolled', ?)
                ''', (staff_db_id, staff_id, device_ip, datetime.datetime.now()))
            except Exception as log_error:
                # Continue even if logging fails
                print(f"Warning: Could not log registration: {log_error}")

            db.commit()
            zk_device.disconnect()

            return jsonify({
                'success': True,
                'message': f'✅ Staff ID {staff_id} successfully registered on ESSL device {device_ip}. Please complete fingerprint scanning on the device to finalize enrollment.',
                'staff_id': staff_id,
                'device_ip': device_ip,
                'enrollment_status': 'ready_for_fingerprint',
                'database_stored': True
            })
        else:
            # Log failed registration (if table exists)
            try:
                db.execute('''
                    INSERT INTO essl_registrations
                    (staff_id, staff_code, device_ip, registration_status, fingerprint_status, error_message, registration_date)
                    VALUES (?, ?, ?, 'failed', 'failed', ?, ?)
                ''', (staff_db_id, staff_id, device_ip, enrollment_result['message'], datetime.datetime.now()))
                db.commit()
            except Exception as log_error:
                print(f"Warning: Could not log failed registration: {log_error}")

            zk_device.disconnect()
            return jsonify({
                'success': False,
                'error': f'Failed to register on ESSL device: {enrollment_result["message"]}'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Registration error: {str(e)}'
        })

# Check Registration Status Route
@app.route('/check_registration_status', methods=['POST'])
@csrf.exempt
def check_registration_status():
    staff_id = request.form.get('staff_id')
    device_ip = request.form.get('device_ip', '*************')

    if not staff_id:
        return jsonify({'success': False, 'error': 'Staff ID required'})

    try:
        # Check database for staff registration
        db = get_db()
        staff_info = db.execute(
            'SELECT id, staff_id, full_name, biometric_enrolled, created_at FROM staff WHERE staff_id = ?',
            (staff_id,)
        ).fetchone()

        if not staff_info:
            return jsonify({
                'success': False,
                'error': 'Staff ID not found in database'
            })

        # Check ESSL device for user enrollment
        zk_device = ZKBiometricDevice(device_ip)

        if not zk_device.connect():
            return jsonify({
                'success': False,
                'error': f'Cannot connect to ESSL device at {device_ip}'
            })

        # Check if user exists on device
        device_users = zk_device.get_users()
        user_on_device = False

        for user in device_users:
            if str(user.user_id) == str(staff_id):
                user_on_device = True
                break

        zk_device.disconnect()

        return jsonify({
            'success': True,
            'staff_id': staff_info['staff_id'],
            'staff_name': staff_info['full_name'],
            'database_enrolled': True,
            'biometric_enrolled': bool(staff_info['biometric_enrolled']),
            'device_enrolled': user_on_device,
            'registration_date': staff_info['created_at'],
            'device_ip': device_ip
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'Status check error: {str(e)}'})



if __name__ == '__main__':
    print("Starting Simplified User Registration & ESSL Fingerprint System...")
    print("Server will be available at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)