# ESSL Registration Dashboard

A simple, focused dashboard for ESSL biometric device registration that allows you to:
- Enter Staff ID and Device IP
- Register staff on ESSL device
- Enroll fingerprints
- Store data in database

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install flask flask-wtf pyzk sqlite3
```

### 2. Run the Application
```bash
python app.py
```

### 3. Access Dashboard
Open your browser and go to: `http://localhost:5000`

## 📋 How to Use

### Registration Process
1. **Enter Staff ID**: Input the unique staff identifier
2. **Enter Device IP**: Input your ESSL device IP address (default: *************)
3. **Enter Staff Name**: Optional staff name field
4. **Click "Register Staff & Enroll Fingerprint"**
5. **Complete fingerprint scanning on the ESSL device**

### Check Registration Status
- Enter Staff ID and Device IP
- Click "Check Registration Status"
- View enrollment status in database and device

## 🔧 Configuration

### ESSL Device Setup
1. Connect your ESSL device to the network
2. Configure device IP (default: *************)
3. Ensure device is accessible from server
4. Default port: 4370

### Database
- Uses SQLite database (`vishnorex.db`)
- Automatically creates required tables
- Stores staff information and registration logs

## 📊 Database Schema

### Staff Table
- `staff_id`: Unique staff identifier
- `full_name`: Staff member name
- `device_ip`: ESSL device IP used for registration
- `biometric_enrolled`: Boolean flag for biometric enrollment
- `fingerprint_enrolled`: Boolean flag for fingerprint enrollment
- `essl_user_id`: User ID on ESSL device

### ESSL Registrations Table
- `staff_code`: Staff ID used for registration
- `device_ip`: Device IP address
- `registration_status`: pending/enrolled/failed
- `fingerprint_status`: pending/enrolled/failed
- `registration_date`: Timestamp of registration

## 🧪 Testing

Run the test suite to verify functionality:
```bash
python test_essl_registration.py
```

Tests include:
- Dashboard page loading
- Staff registration
- Status checking
- Multiple staff registration
- Invalid input handling

## 🔌 ESSL Device Integration

### Supported Features
- Staff enrollment on device
- Fingerprint enrollment trigger
- User verification
- Device connectivity check

### Device Requirements
- ESSL ZK-compatible biometric device
- Ethernet connectivity
- TCP/IP communication on port 4370

## 📁 File Structure

```
├── app.py                      # Main Flask application
├── database.py                 # Database setup and schema
├── zk_biometric.py            # ESSL device integration
├── templates/
│   └── essl_dashboard.html    # Dashboard interface
├── test_essl_registration.py  # Test suite
└── vishnorex.db              # SQLite database
```

## 🔍 API Endpoints

### POST /register_staff
Register staff and enroll fingerprint
- Parameters: `staff_id`, `device_ip`, `staff_name`
- Returns: Success/error status and enrollment details

### POST /check_registration_status
Check staff registration status
- Parameters: `staff_id`, `device_ip`
- Returns: Database and device enrollment status

## ⚠️ Important Notes

1. **Device Connectivity**: Ensure ESSL device is connected and accessible
2. **IP Configuration**: Verify correct device IP address
3. **Fingerprint Enrollment**: Complete fingerprint scanning on device after registration
4. **Database Storage**: All registrations are automatically stored in database
5. **Error Handling**: Check status messages for troubleshooting

## 🛠️ Troubleshooting

### Common Issues

**Cannot connect to device**
- Check device IP address
- Verify network connectivity
- Ensure device is powered on

**Registration fails**
- Verify staff ID format
- Check device capacity
- Ensure device is not in use

**Fingerprint enrollment issues**
- Follow device prompts
- Ensure clean finger placement
- Retry if initial scan fails

## 📞 Support

For issues or questions:
1. Check error messages in dashboard
2. Review test results
3. Verify device connectivity
4. Check database logs

## 🔄 Updates

This dashboard focuses specifically on:
- Staff ID input
- Device IP configuration
- Fingerprint enrollment
- Database storage
- Simple, clean interface

No additional features beyond ESSL registration requirements.
